"""
VuManChu Cipher B Parameters
All Pine Script input variables and constants extracted and organized by component.
"""

from dataclasses import dataclass
from typing import Optional, Union
import numpy as np

@dataclass
class WaveTrendParams:
    """WaveTrend Oscillator Parameters"""
    # Display settings
    wt_show: bool = True
    wt_buy_show: bool = True
    wt_gold_show: bool = True
    wt_sell_show: bool = True
    wt_div_show: bool = True
    vwap_show: bool = True
    
    # Core calculation parameters
    wt_channel_len: int = 9
    wt_average_len: int = 12
    wt_ma_source: str = 'hlc3'  # hlc3, close, open, high, low
    wt_ma_len: int = 3
    
    # Overbought/Oversold levels
    ob_level: float = 53.0
    ob_level2: float = 60.0
    ob_level3: float = 100.0
    os_level: float = -53.0
    os_level2: float = -60.0
    os_level3: float = -75.0
    
    # Divergence settings
    wt_show_div: bool = True
    wt_show_hidden_div: bool = False
    show_hidden_div_nl: bool = True
    wt_div_ob_level: float = 45.0
    wt_div_os_level: float = -65.0
    
    # Additional divergence range
    wt_div_ob_level_add_show: bool = True
    wt_div_ob_level_add: float = 15.0
    wt_div_os_level_add: float = -40.0

@dataclass
class RSIParams:
    """RSI Parameters"""
    rsi_show: bool = True
    rsi_src: str = 'close'
    rsi_len: int = 14
    rsi_overbought: float = 60.0
    rsi_oversold: float = 30.0

    # RSI Divergence parameters
    rsi_show_div: bool = False
    rsi_show_hidden_div: bool = False
    rsi_div_ob_level: float = 60.0
    rsi_div_os_level: float = 30.0

@dataclass
class MFIParams:
    """Money Flow Index Parameters"""
    rsi_mfi_show: bool = True
    rsi_mfi_period: int = 60
    rsi_mfi_multiplier: int = 150
    rsi_mfi_pos_y: float = 2.5

@dataclass
class StochasticParams:
    """Stochastic RSI Parameters"""
    stoch_show: bool = True
    stoch_use_log: bool = True
    stoch_src: str = 'close'
    stoch_len: int = 14
    stoch_rsi_len: int = 14
    stoch_k_smooth: int = 3
    stoch_d_smooth: int = 3
    stoch_avg: bool = False

    # Stochastic Divergence parameters
    stoch_show_div: bool = False
    stoch_show_hidden_div: bool = False

@dataclass
class SchaffParams:
    """Schaff Trend Cycle Parameters"""
    tc_line: bool = False
    tc_src: str = 'close'
    tc_length: int = 10
    tc_fast_length: int = 23
    tc_slow_length: int = 50
    tc_factor: float = 0.5

@dataclass
class SommiParams:
    """Sommi Flag and Diamond Parameters"""
    # Sommi Flag
    sommi_flag_show: bool = False
    sommi_show_vwap: bool = False
    sommi_vwap_tf: str = '720'
    sommi_vwap_bear_level: float = 0.0
    sommi_vwap_bull_level: float = 0.0
    sommi_flag_wt_bear_level: float = 0.0
    sommi_flag_wt_bull_level: float = 0.0
    sommi_rsi_mfi_bear_level: float = 0.0
    sommi_rsi_mfi_bull_level: float = 0.0

    # Sommi Diamond
    sommi_diamond_show: bool = False
    sommi_htc_res: str = '60'
    sommi_htc_res2: str = '240'
    sommi_diamond_wt_bear_level: float = 0.0
    sommi_diamond_wt_bull_level: float = 0.0

@dataclass
class MACDParams:
    """MACD Parameters for WT Colors"""
    macd_wt_colors_show: bool = False
    macd_wt_colors_tf: str = '240'

@dataclass
class DivergenceParams:
    """Divergence Detection Parameters"""
    # Lookback periods
    lbR: int = 5
    lbL: int = 5
    
    # Range settings
    rangeUpper: int = 60
    rangeLower: int = 5
    
    # Plot settings
    plotBull: bool = True
    plotHiddenBull: bool = False
    plotBear: bool = True
    plotHiddenBear: bool = False

@dataclass
class ColorParams:
    """Color Settings (hex values for visualization)"""
    # WaveTrend colors
    colorWT1blue: str = '#4994ec'
    colorWT2purple: str = '#1f1559'
    VWAPColor: str = '#ffffff'
    
    # RSI colors
    rsi_ob_color: str = '#e13e3e'
    rsi_os_color: str = '#3ee145'
    rsi_na_color: str = '#c33ee1'
    
    # MFI colors
    rsi_mfi_color_above: str = '#3ee145'
    rsi_mfi_color_below: str = '#ff3d2e'
    
    # General colors
    colorWhite: str = '#ffffff'
    colorGreen: str = '#00ff00'
    colorRed: str = '#ff0000'
    colorYellow: str = '#ffff00'

@dataclass
class DisplayParams:
    """Display and UI Parameters"""
    dark_mode: bool = False
    
    # Signal display
    buy_signal_show: bool = True
    sell_signal_show: bool = True
    gold_signal_show: bool = True

@dataclass
class VuManChuParams:
    """Complete VuManChu Cipher B Parameter Set"""
    wavetrend: WaveTrendParams = WaveTrendParams()
    rsi: RSIParams = RSIParams()
    mfi: MFIParams = MFIParams()
    stochastic: StochasticParams = StochasticParams()
    schaff: SchaffParams = SchaffParams()
    sommi: SommiParams = SommiParams()
    macd: MACDParams = MACDParams()
    divergence: DivergenceParams = DivergenceParams()
    colors: ColorParams = ColorParams()
    display: DisplayParams = DisplayParams()
    
    def validate(self) -> bool:
        """Validate all parameter ranges and types"""
        try:
            # WaveTrend validation
            assert 1 <= self.wavetrend.wt_channel_len <= 100, "WT Channel Length must be 1-100"
            assert 1 <= self.wavetrend.wt_average_len <= 100, "WT Average Length must be 1-100"
            assert 1 <= self.wavetrend.wt_ma_len <= 50, "WT MA Length must be 1-50"
            
            # RSI validation
            assert 1 <= self.rsi.rsi_len <= 100, "RSI Length must be 1-100"
            assert 0 <= self.rsi.rsi_overbought <= 100, "RSI Overbought must be 0-100"
            assert 0 <= self.rsi.rsi_oversold <= 100, "RSI Oversold must be 0-100"
            
            # MFI validation
            assert 1 <= self.mfi.rsi_mfi_period <= 200, "MFI Period must be 1-200"
            assert self.mfi.rsi_mfi_multiplier > 0, "MFI Multiplier must be positive"
            
            return True
        except AssertionError as e:
            print(f"Parameter validation failed: {e}")
            return False

# Default parameter instance
DEFAULT_PARAMS = VuManChuParams()

def get_hlc3_source(high: np.ndarray, low: np.ndarray, close: np.ndarray) -> np.ndarray:
    """Calculate HLC3 (typical price) source"""
    return (high + low + close) / 3.0

def get_price_source(source: str, open_: np.ndarray, high: np.ndarray, 
                    low: np.ndarray, close: np.ndarray) -> np.ndarray:
    """Get price source array based on string identifier"""
    sources = {
        'open': open_,
        'high': high,
        'low': low,
        'close': close,
        'hlc3': get_hlc3_source(high, low, close),
        'hl2': (high + low) / 2.0,
        'ohlc4': (open_ + high + low + close) / 4.0
    }
    
    if source not in sources:
        raise ValueError(f"Unknown price source: {source}. Available: {list(sources.keys())}")
    
    return sources[source]