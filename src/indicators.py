import numpy as np
import pandas as pd
import talib as ta
from typing import Tuple, Optional
from src.parameters import VuManChuParams, get_price_source, DEFAULT_PARAMS

def f_wavetrend(
    src: np.ndarray,
    params: Optional[VuManChuParams] = None,
    tf: Optional[str] = None
) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
    """
    WaveTrend Oscillator - Core indicator for VuManChu Cipher B
    
    Args:
        src: Price source data (typically HLC3)
        params: VuManChu parameter set (uses DEFAULT_PARAMS if None)
        tf: Timeframe (None for current timeframe)
        
    Returns:
        Tuple containing: (wt1, wt2, wtOversold, wtOverbought, wtCross, 
                          wtCrossUp, wtCrossDown, wtCrossLast, wtCrossUpLast, 
                          wtCrossDownLast, wtVwap)
    """
    if params is None:
        params = DEFAULT_PARAMS
    
    wt_params = params.wavetrend
    
    # Handle multi-timeframe data (simplified for current timeframe)
    tfsrc = src.copy()
    
    # Calculate ESA (Exponential Smoothed Average)
    esa = ta.EMA(tfsrc, timeperiod=wt_params.wt_channel_len)
    
    # Calculate DE (Deviation)
    de = ta.EMA(np.abs(tfsrc - esa), timeperiod=wt_params.wt_channel_len)
    
    # Calculate CI (Channel Index) with Pine Script's 0.015 factor
    ci = np.where(de != 0, (tfsrc - esa) / (0.015 * de), 0)
    
    # Calculate WT1 (WaveTrend 1)
    wt1 = ta.EMA(ci, timeperiod=wt_params.wt_average_len)
    
    # Calculate WT2 (WaveTrend 2) 
    wt2 = ta.SMA(wt1, timeperiod=wt_params.wt_ma_len)
    
    # Calculate VWAP-style difference
    wt_vwap = wt1 - wt2
    
    # Overbought/Oversold conditions
    wt_oversold = (wt2 <= wt_params.os_level).astype(bool)
    wt_overbought = (wt2 >= wt_params.ob_level).astype(bool)
    
    # Cross detection (Pine Script compatible)
    wt_cross = np.zeros_like(wt1, dtype=bool)
    wt_cross_up = np.zeros_like(wt1, dtype=bool)
    wt_cross_down = np.zeros_like(wt1, dtype=bool)

    # Calculate crosses using Pine Script logic
    for i in range(1, len(wt1)):
        if not (np.isnan(wt1[i]) or np.isnan(wt2[i]) or np.isnan(wt1[i-1]) or np.isnan(wt2[i-1])):
            # Pine Script cross detection: cross(wt1, wt2)
            prev_diff = wt1[i-1] - wt2[i-1]
            curr_diff = wt1[i] - wt2[i]

            # Cross occurred if signs are different (Pine Script cross() function)
            if (prev_diff > 0 and curr_diff <= 0) or (prev_diff < 0 and curr_diff >= 0):
                wt_cross[i] = True

            # Pine Script logic: wtCrossUp = wt2 - wt1 <= 0 (when cross occurs)
            # Pine Script logic: wtCrossDown = wt2 - wt1 >= 0 (when cross occurs)
            if wt_cross[i]:
                wt_cross_up[i] = (wt2[i] - wt1[i]) <= 0  # WT1 >= WT2
                wt_cross_down[i] = (wt2[i] - wt1[i]) >= 0  # WT1 <= WT2
    
    # Cross detection (2 periods ago) - for Pine Script compatibility
    wt_cross_last = np.roll(wt_cross, 2)
    wt_cross_up_last = np.roll(wt_cross_up, 2)
    wt_cross_down_last = np.roll(wt_cross_down, 2)
    
    # Set first two elements to False to avoid roll artifacts
    wt_cross_last[:2] = False
    wt_cross_up_last[:2] = False
    wt_cross_down_last[:2] = False
    
    return (wt1, wt2, wt_oversold, wt_overbought, wt_cross,
            wt_cross_up, wt_cross_down, wt_cross_last, wt_cross_up_last,
            wt_cross_down_last, wt_vwap)


def f_rsimfi(
    open_: np.ndarray,
    high: np.ndarray,
    low: np.ndarray,
    close: np.ndarray,
    params: Optional[VuManChuParams] = None,
    tf: Optional[str] = None
) -> np.ndarray:
    """
    RSI + MFI Area calculation - Pine Script compatible momentum indicator

    Pine Script: f_rsimfi(_period, _multiplier, _tf) =>
        security(syminfo.tickerid, _tf, sma(((close - open) / (high - low)) * _multiplier, _period) - rsiMFIPosY)

    Args:
        open_: Open price data
        high: High price data
        low: Low price data
        close: Close price data
        params: VuManChu parameter set (uses DEFAULT_PARAMS if None)
        tf: Timeframe (None for current timeframe)

    Returns:
        RSI+MFI area values
    """
    if params is None:
        params = DEFAULT_PARAMS

    mfi_params = params.mfi

    # Pine Script calculation: ((close - open) / (high - low)) * multiplier
    # Avoid division by zero
    price_range = high - low
    price_momentum = np.where(
        price_range != 0,
        ((close - open_) / price_range) * mfi_params.rsi_mfi_multiplier,
        0
    )

    # Apply SMA smoothing
    rsi_mfi_raw = ta.SMA(price_momentum, timeperiod=mfi_params.rsi_mfi_period)

    # Subtract position Y offset (Pine Script: - rsiMFIPosY)
    rsi_mfi = rsi_mfi_raw - mfi_params.rsi_mfi_pos_y

    return rsi_mfi


def f_stochrsi(
    src: np.ndarray,
    params: Optional[VuManChuParams] = None
) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """
    Stochastic RSI calculation with K and D smoothing

    Args:
        src: Price source data
        params: VuManChu parameter set (uses DEFAULT_PARAMS if None)

    Returns:
        Tuple containing: (stoch_k, stoch_d, stoch_avg)
    """
    if params is None:
        params = DEFAULT_PARAMS

    stoch_params = params.stochastic

    # Calculate RSI first
    rsi = ta.RSI(src, timeperiod=stoch_params.stoch_rsi_len)

    # Apply logarithmic transformation if enabled
    if stoch_params.stoch_use_log:
        rsi = np.log(rsi + 1e-10)  # Add small value to avoid log(0)

    # Calculate Stochastic of RSI
    # Get rolling min and max of RSI
    rsi_series = pd.Series(rsi)
    rsi_min = rsi_series.rolling(window=stoch_params.stoch_len).min().values
    rsi_max = rsi_series.rolling(window=stoch_params.stoch_len).max().values

    # Calculate %K
    stoch_k_raw = np.where(
        (rsi_max - rsi_min) != 0,
        100 * (rsi - rsi_min) / (rsi_max - rsi_min),
        50  # Default to 50 when range is 0
    )

    # Smooth %K
    stoch_k = ta.SMA(stoch_k_raw, timeperiod=stoch_params.stoch_k_smooth)

    # Calculate %D (smoothed %K)
    stoch_d = ta.SMA(stoch_k, timeperiod=stoch_params.stoch_d_smooth)

    # Calculate average of K and D if enabled
    stoch_avg = (stoch_k + stoch_d) / 2.0 if stoch_params.stoch_avg else stoch_k

    return stoch_k, stoch_d, stoch_avg


def f_tc(
    src: np.ndarray,
    params: Optional[VuManChuParams] = None
) -> np.ndarray:
    """
    Schaff Trend Cycle (STC) calculation

    Args:
        src: Price source data
        params: VuManChu parameter set (uses DEFAULT_PARAMS if None)

    Returns:
        Schaff Trend Cycle values
    """
    if params is None:
        params = DEFAULT_PARAMS

    schaff_params = params.schaff

    # Calculate MACD using EMAs
    fast_ema = ta.EMA(src, timeperiod=schaff_params.tc_fast_length)
    slow_ema = ta.EMA(src, timeperiod=schaff_params.tc_slow_length)
    macd = fast_ema - slow_ema

    # Calculate Stochastic of MACD
    macd_series = pd.Series(macd)
    macd_min = macd_series.rolling(window=schaff_params.tc_length).min().values
    macd_max = macd_series.rolling(window=schaff_params.tc_length).max().values

    # Calculate %K of MACD
    stoch_macd = np.where(
        (macd_max - macd_min) != 0,
        100 * (macd - macd_min) / (macd_max - macd_min),
        50
    )

    # Apply factor smoothing
    factor = schaff_params.tc_factor
    stc = np.zeros_like(stoch_macd)

    # Initialize first value
    stc[0] = stoch_macd[0] if not np.isnan(stoch_macd[0]) else 50

    # Apply exponential smoothing with factor
    for i in range(1, len(stoch_macd)):
        if not np.isnan(stoch_macd[i]):
            stc[i] = stc[i-1] + factor * (stoch_macd[i] - stc[i-1])
        else:
            stc[i] = stc[i-1]

    # Second stochastic calculation on the result
    stc_series = pd.Series(stc)
    stc_min = stc_series.rolling(window=schaff_params.tc_length).min().values
    stc_max = stc_series.rolling(window=schaff_params.tc_length).max().values

    final_stc = np.where(
        (stc_max - stc_min) != 0,
        100 * (stc - stc_min) / (stc_max - stc_min),
        50
    )

    # Final smoothing
    result = np.zeros_like(final_stc)
    result[0] = final_stc[0] if not np.isnan(final_stc[0]) else 50

    for i in range(1, len(final_stc)):
        if not np.isnan(final_stc[i]):
            result[i] = result[i-1] + factor * (final_stc[i] - result[i-1])
        else:
            result[i] = result[i-1]

    return result


def f_top_fractal(src: np.ndarray, len_left: int, len_right: int) -> np.ndarray:
    """
    Detect top fractals (local maxima)

    Args:
        src: Price data array
        len_left: Number of bars to look left
        len_right: Number of bars to look right

    Returns:
        Boolean array indicating fractal positions
    """
    fractals = np.zeros(len(src), dtype=bool)

    for i in range(len_left, len(src) - len_right):
        if np.isnan(src[i]):
            continue

        is_fractal = True
        current_value = src[i]

        # Check left side
        for j in range(i - len_left, i):
            if np.isnan(src[j]) or src[j] >= current_value:
                is_fractal = False
                break

        # Check right side
        if is_fractal:
            for j in range(i + 1, i + len_right + 1):
                if np.isnan(src[j]) or src[j] >= current_value:
                    is_fractal = False
                    break

        fractals[i] = is_fractal

    return fractals


def f_bot_fractal(src: np.ndarray, len_left: int, len_right: int) -> np.ndarray:
    """
    Detect bottom fractals (local minima)

    Args:
        src: Price data array
        len_left: Number of bars to look left
        len_right: Number of bars to look right

    Returns:
        Boolean array indicating fractal positions
    """
    fractals = np.zeros(len(src), dtype=bool)

    for i in range(len_left, len(src) - len_right):
        if np.isnan(src[i]):
            continue

        is_fractal = True
        current_value = src[i]

        # Check left side
        for j in range(i - len_left, i):
            if np.isnan(src[j]) or src[j] <= current_value:
                is_fractal = False
                break

        # Check right side
        if is_fractal:
            for j in range(i + 1, i + len_right + 1):
                if np.isnan(src[j]) or src[j] <= current_value:
                    is_fractal = False
                    break

        fractals[i] = is_fractal

    return fractals


def f_fractalize(src: np.ndarray, len_left: int, len_right: int) -> Tuple[np.ndarray, np.ndarray]:
    """
    Detect both top and bottom fractals

    Args:
        src: Price data array
        len_left: Number of bars to look left
        len_right: Number of bars to look right

    Returns:
        Tuple containing: (top_fractals, bottom_fractals)
    """
    top_fractals = f_top_fractal(src, len_left, len_right)
    bottom_fractals = f_bot_fractal(src, len_left, len_right)

    return top_fractals, bottom_fractals


def f_findDivs(
    price: np.ndarray,
    indicator: np.ndarray,
    params: Optional[VuManChuParams] = None
) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
    """
    Find regular and hidden divergences between price and indicator

    Args:
        price: Price data array (typically high for bearish, low for bullish)
        indicator: Indicator data array (e.g., WaveTrend, RSI)
        params: VuManChu parameter set (uses DEFAULT_PARAMS if None)

    Returns:
        Tuple containing: (bullish_div, bearish_div, hidden_bullish_div, hidden_bearish_div)
    """
    if params is None:
        params = DEFAULT_PARAMS

    div_params = params.divergence

    # Get fractals
    price_top_fractals, price_bot_fractals = f_fractalize(price, div_params.lbL, div_params.lbR)
    ind_top_fractals, ind_bot_fractals = f_fractalize(indicator, div_params.lbL, div_params.lbR)

    # Initialize divergence arrays
    bullish_div = np.zeros(len(price), dtype=bool)
    bearish_div = np.zeros(len(price), dtype=bool)
    hidden_bullish_div = np.zeros(len(price), dtype=bool)
    hidden_bearish_div = np.zeros(len(price), dtype=bool)

    # Find divergences
    for i in range(div_params.rangeUpper, len(price)):
        # Look for previous fractals within range
        start_idx = max(0, i - div_params.rangeUpper)
        end_idx = max(0, i - div_params.rangeLower)

        # Bullish divergence: Price makes lower low, indicator makes higher low
        if price_bot_fractals[i] and ind_bot_fractals[i]:
            for j in range(start_idx, end_idx):
                if price_bot_fractals[j] and ind_bot_fractals[j]:
                    # Regular bullish divergence
                    if price[i] < price[j] and indicator[i] > indicator[j]:
                        bullish_div[i] = True
                    # Hidden bullish divergence
                    elif price[i] > price[j] and indicator[i] < indicator[j]:
                        hidden_bullish_div[i] = True
                    break

        # Bearish divergence: Price makes higher high, indicator makes lower high
        if price_top_fractals[i] and ind_top_fractals[i]:
            for j in range(start_idx, end_idx):
                if price_top_fractals[j] and ind_top_fractals[j]:
                    # Regular bearish divergence
                    if price[i] > price[j] and indicator[i] < indicator[j]:
                        bearish_div[i] = True
                    # Hidden bearish divergence
                    elif price[i] < price[j] and indicator[i] > indicator[j]:
                        hidden_bearish_div[i] = True
                    break

    return bullish_div, bearish_div, hidden_bullish_div, hidden_bearish_div


def f_findSommiFlag(
    wt1: np.ndarray,
    wt2: np.ndarray,
    rsi_mfi: np.ndarray,
    high: np.ndarray,
    low: np.ndarray, 
    close: np.ndarray,
    params: Optional[VuManChuParams] = None
) -> Tuple[np.ndarray, np.ndarray]:
    """
    Find Sommi Flag patterns (bearish and bullish)
    
    Replicates Pine Script's f_findSommiFlag which combines:
    - Current timeframe: RSI+MFI, WaveTrend crosses
    - Higher timeframe (12h): VWAP from WaveTrend calculation
    
    Args:
        wt1: Current timeframe WaveTrend 1 values
        wt2: Current timeframe WaveTrend 2 values  
        rsi_mfi: RSI+MFI area values
        high: High price data for HTF calculation
        low: Low price data for HTF calculation
        close: Close price data for HTF calculation
        params: VuManChu parameter set
        
    Returns:
        Tuple containing: (sommi_flag_bearish, sommi_flag_bullish)
    """
    if params is None:
        params = DEFAULT_PARAMS

    sommi_params = params.sommi
    
    # Calculate higher timeframe WaveTrend to get VWAP (replicates Pine Script tf parameter)
    wt_src = get_price_source(params.wavetrend.wt_ma_source, close, high, low, close)
    
    # TODO: Implement proper higher timeframe data conversion for sommiVwapTF ('720' = 12h)
    # For now, calculate with current timeframe data - this is a limitation
    (_, _, _, _, _, _, _, _, _, _, htf_vwap) = f_wavetrend(wt_src, params)
    
    # Initialize flag arrays
    sommi_flag_bearish = np.zeros(len(wt1), dtype=bool)
    sommi_flag_bullish = np.zeros(len(wt1), dtype=bool)

    # Calculate current timeframe WaveTrend crosses
    wt_cross_down = np.zeros(len(wt1), dtype=bool)
    wt_cross_up = np.zeros(len(wt1), dtype=bool)

    for i in range(1, len(wt1)):
        if not (np.isnan(wt1[i]) or np.isnan(wt2[i]) or np.isnan(wt1[i-1]) or np.isnan(wt2[i-1])):
            prev_diff = wt1[i-1] - wt2[i-1]
            curr_diff = wt1[i] - wt2[i]

            wt_cross_down[i] = (prev_diff > 0 and curr_diff < 0)
            wt_cross_up[i] = (prev_diff < 0 and curr_diff > 0)

    # Sommi Flag pattern detection
    for i in range(len(wt1)):
        # Bearish Flag: RSI+MFI < 0, WT2 > bear_level, WT crosses down, HTF_VWAP < bear_level
        if (rsi_mfi[i] < sommi_params.sommi_rsi_mfi_bear_level and
            wt2[i] > sommi_params.sommi_flag_wt_bear_level and
            wt_cross_down[i] and
            htf_vwap[i] < sommi_params.sommi_vwap_bear_level):
            sommi_flag_bearish[i] = True

        # Bullish Flag: RSI+MFI > 0, WT2 < bull_level, WT crosses up, HTF_VWAP > bull_level  
        if (rsi_mfi[i] > sommi_params.sommi_rsi_mfi_bull_level and
            wt2[i] < sommi_params.sommi_flag_wt_bull_level and
            wt_cross_up[i] and
            htf_vwap[i] > sommi_params.sommi_vwap_bull_level):
            sommi_flag_bullish[i] = True

    return sommi_flag_bearish, sommi_flag_bullish


def f_findSommiDiamond(
    wt1: np.ndarray,
    wt2: np.ndarray,
    close: np.ndarray,
    params: Optional[VuManChuParams] = None
) -> Tuple[np.ndarray, np.ndarray]:
    """
    Find Sommi Diamond patterns (bearish and bullish)

    Args:
        wt1: WaveTrend 1 values
        wt2: WaveTrend 2 values
        close: Close price data
        params: VuManChu parameter set (uses DEFAULT_PARAMS if None)

    Returns:
        Tuple containing: (sommi_diamond_bearish, sommi_diamond_bullish)
    """
    if params is None:
        params = DEFAULT_PARAMS

    sommi_params = params.sommi

    # Initialize diamond arrays
    sommi_diamond_bearish = np.zeros(len(wt1), dtype=bool)
    sommi_diamond_bullish = np.zeros(len(wt1), dtype=bool)

    # Calculate Heiken Ashi candles (simplified)
    ha_close = (close + close + close + close) / 4  # Simplified HA close
    ha_open = np.zeros_like(close)
    ha_open[0] = close[0]

    for i in range(1, len(close)):
        ha_open[i] = (ha_open[i-1] + ha_close[i-1]) / 2

    # Determine candle color
    ha_red = ha_close < ha_open
    ha_green = ha_close > ha_open

    # Calculate WaveTrend crosses
    wt_cross_down = np.zeros(len(wt1), dtype=bool)
    wt_cross_up = np.zeros(len(wt1), dtype=bool)

    for i in range(1, len(wt1)):
        if not (np.isnan(wt1[i]) or np.isnan(wt2[i]) or np.isnan(wt1[i-1]) or np.isnan(wt2[i-1])):
            prev_diff = wt1[i-1] - wt2[i-1]
            curr_diff = wt1[i] - wt2[i]

            wt_cross_down[i] = (prev_diff > 0 and curr_diff < 0)
            wt_cross_up[i] = (prev_diff < 0 and curr_diff > 0)

    # Sommi Diamond conditions
    for i in range(len(wt1)):
        # Bearish Diamond: HT Candle is red, WT > 0 and crossed down
        if (ha_red[i] and
            wt1[i] > sommi_params.sommi_diamond_wt_bear_level and
            wt_cross_down[i]):
            sommi_diamond_bearish[i] = True

        # Bullish Diamond: HT Candle is green, WT < 0 and crossed up
        if (ha_green[i] and
            wt1[i] < sommi_params.sommi_diamond_wt_bull_level and
            wt_cross_up[i]):
            sommi_diamond_bullish[i] = True

    return sommi_diamond_bearish, sommi_diamond_bullish


def generate_signals(
    high: np.ndarray,
    low: np.ndarray,
    close: np.ndarray,
    params: Optional[VuManChuParams] = None
) -> dict:
    """
    Generate all VuManChu Cipher B signals and indicators

    Args:
        high: High price data
        low: Low price data
        close: Close price data
        params: VuManChu parameter set (uses DEFAULT_PARAMS if None)

    Returns:
        Dictionary containing all calculated indicators and signals
    """
    if params is None:
        params = DEFAULT_PARAMS

    from src.parameters import get_price_source

    # Get price source for WaveTrend
    wt_src = get_price_source(params.wavetrend.wt_ma_source, close, high, low, close)

    # Calculate WaveTrend
    (wt1, wt2, wt_oversold, wt_overbought, wt_cross,
     wt_cross_up, wt_cross_down, wt_cross_last, wt_cross_up_last,
     wt_cross_down_last, wt_vwap) = f_wavetrend(wt_src, params)

    # Calculate RSI + MFI
    rsi_mfi = f_rsimfi(close, high, low, close, params)  # Note: using close for open as approximation

    # Calculate Stochastic RSI
    stoch_k, stoch_d, stoch_avg = f_stochrsi(close, params)

    # Calculate Schaff Trend Cycle
    stc = f_tc(close, params)

    # Calculate divergences
    bullish_div, bearish_div, hidden_bullish_div, hidden_bearish_div = f_findDivs(
        close, wt2, params
    )

    # Calculate Sommi patterns
    sommi_flag_bearish, sommi_flag_bullish = f_findSommiFlag(
        wt1, wt2, rsi_mfi, high, low, close, params
    )
    sommi_diamond_bearish, sommi_diamond_bullish = f_findSommiDiamond(
        wt1, wt2, close, params
    )

    # Generate buy/sell signals
    buy_signals = np.zeros(len(close), dtype=bool)
    sell_signals = np.zeros(len(close), dtype=bool)
    gold_signals = np.zeros(len(close), dtype=bool)

    # Calculate RSI for gold signal condition
    rsi = ta.RSI(close, timeperiod=params.rsi.rsi_len)

    for i in range(len(close)):
        # Buy signals: WT cross up at oversold levels
        if (wt_cross_up[i] and wt_oversold[i] and
            params.wavetrend.wt_buy_show):
            buy_signals[i] = True

        # Sell signals: WT cross down at overbought levels
        if (wt_cross_down[i] and wt_overbought[i] and
            params.wavetrend.wt_sell_show):
            sell_signals[i] = True

        # Gold signals: RSI < 20, WT <= -80, cross up, with bullish divergence
        if (rsi[i] < 20 and wt2[i] <= -80 and wt_cross_up[i] and
            bullish_div[i] and params.wavetrend.wt_gold_show):
            gold_signals[i] = True

    # Compile all results
    results = {
        # WaveTrend components
        'wt1': wt1,
        'wt2': wt2,
        'wt_vwap': wt_vwap,
        'wt_oversold': wt_oversold,
        'wt_overbought': wt_overbought,
        'wt_cross': wt_cross,
        'wt_cross_up': wt_cross_up,
        'wt_cross_down': wt_cross_down,

        # RSI + MFI
        'rsi_mfi': rsi_mfi,
        'rsi': rsi,

        # Stochastic RSI
        'stoch_k': stoch_k,
        'stoch_d': stoch_d,
        'stoch_avg': stoch_avg,

        # Schaff Trend Cycle
        'stc': stc,

        # Divergences
        'bullish_div': bullish_div,
        'bearish_div': bearish_div,
        'hidden_bullish_div': hidden_bullish_div,
        'hidden_bearish_div': hidden_bearish_div,

        # Sommi patterns
        'sommi_flag_bearish': sommi_flag_bearish,
        'sommi_flag_bullish': sommi_flag_bullish,
        'sommi_diamond_bearish': sommi_diamond_bearish,
        'sommi_diamond_bullish': sommi_diamond_bullish,

        # Trading signals
        'buy_signals': buy_signals,
        'sell_signals': sell_signals,
        'gold_signals': gold_signals,

        # Price data
        'high': high,
        'low': low,
        'close': close
    }

    return results
