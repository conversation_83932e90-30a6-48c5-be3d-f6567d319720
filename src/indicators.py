import numpy as np
import pandas as pd
import talib as ta
from typing import Tuple, Optional
from src.parameters import VuManChuParams, get_price_source, DEFAULT_PARAMS

def f_wavetrend(
    src: np.ndarray,
    params: Optional[VuManChuParams] = None,
    tf: Optional[str] = None
) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
    """
    WaveTrend Oscillator - Core indicator for VuManChu Cipher B
    
    Args:
        src: Price source data (typically HLC3)
        params: VuManChu parameter set (uses DEFAULT_PARAMS if None)
        tf: Timeframe (None for current timeframe)
        
    Returns:
        Tuple containing: (wt1, wt2, wtOversold, wtOverbought, wtCross, 
                          wtCrossUp, wtCrossDown, wtCrossLast, wtCrossUpLast, 
                          wtCrossDownLast, wtVwap)
    """
    if params is None:
        params = DEFAULT_PARAMS
    
    wt_params = params.wavetrend
    
    # Handle multi-timeframe data (simplified for current timeframe)
    tfsrc = src.copy()
    
    # Calculate ESA (Exponential Smoothed Average)
    esa = ta.EMA(tfsrc, timeperiod=wt_params.wt_channel_len)
    
    # Calculate DE (Deviation)
    de = ta.EMA(np.abs(tfsrc - esa), timeperiod=wt_params.wt_channel_len)
    
    # Calculate CI (Channel Index) with Pine Script's 0.015 factor
    ci = np.where(de != 0, (tfsrc - esa) / (0.015 * de), 0)
    
    # Calculate WT1 (WaveTrend 1)
    wt1 = ta.EMA(ci, timeperiod=wt_params.wt_average_len)
    
    # Calculate WT2 (WaveTrend 2) 
    wt2 = ta.SMA(wt1, timeperiod=wt_params.wt_ma_len)
    
    # Calculate VWAP-style difference
    wt_vwap = wt1 - wt2
    
    # Overbought/Oversold conditions
    wt_oversold = (wt2 <= wt_params.os_level).astype(bool)
    wt_overbought = (wt2 >= wt_params.ob_level).astype(bool)
    
    # Cross detection (Pine Script compatible)
    wt_cross = np.zeros_like(wt1, dtype=bool)
    wt_cross_up = np.zeros_like(wt1, dtype=bool)
    wt_cross_down = np.zeros_like(wt1, dtype=bool)

    # Calculate crosses using Pine Script logic
    for i in range(1, len(wt1)):
        if not (np.isnan(wt1[i]) or np.isnan(wt2[i]) or np.isnan(wt1[i-1]) or np.isnan(wt2[i-1])):
            # Pine Script cross detection: cross(wt1, wt2)
            prev_diff = wt1[i-1] - wt2[i-1]
            curr_diff = wt1[i] - wt2[i]

            # Cross occurred if signs are different (Pine Script cross() function)
            if (prev_diff > 0 and curr_diff <= 0) or (prev_diff < 0 and curr_diff >= 0):
                wt_cross[i] = True

            # Pine Script logic: wtCrossUp = wt2 - wt1 <= 0 (when cross occurs)
            # Pine Script logic: wtCrossDown = wt2 - wt1 >= 0 (when cross occurs)
            if wt_cross[i]:
                wt_cross_up[i] = (wt2[i] - wt1[i]) <= 0  # WT1 >= WT2
                wt_cross_down[i] = (wt2[i] - wt1[i]) >= 0  # WT1 <= WT2
    
    # Cross detection (2 periods ago) - for Pine Script compatibility
    wt_cross_last = np.roll(wt_cross, 2)
    wt_cross_up_last = np.roll(wt_cross_up, 2)
    wt_cross_down_last = np.roll(wt_cross_down, 2)
    
    # Set first two elements to False to avoid roll artifacts
    wt_cross_last[:2] = False
    wt_cross_up_last[:2] = False
    wt_cross_down_last[:2] = False
    
    return (wt1, wt2, wt_oversold, wt_overbought, wt_cross,
            wt_cross_up, wt_cross_down, wt_cross_last, wt_cross_up_last,
            wt_cross_down_last, wt_vwap)


def f_rsimfi(
    open_: np.ndarray,
    high: np.ndarray,
    low: np.ndarray,
    close: np.ndarray,
    params: Optional[VuManChuParams] = None,
    tf: Optional[str] = None
) -> np.ndarray:
    """
    RSI + MFI Area calculation - Pine Script compatible momentum indicator

    Pine Script: f_rsimfi(_period, _multiplier, _tf) =>
        security(syminfo.tickerid, _tf, sma(((close - open) / (high - low)) * _multiplier, _period) - rsiMFIPosY)

    Args:
        open_: Open price data
        high: High price data
        low: Low price data
        close: Close price data
        params: VuManChu parameter set (uses DEFAULT_PARAMS if None)
        tf: Timeframe (None for current timeframe)

    Returns:
        RSI+MFI area values
    """
    if params is None:
        params = DEFAULT_PARAMS

    mfi_params = params.mfi

    # Pine Script calculation: ((close - open) / (high - low)) * multiplier
    # Avoid division by zero
    price_range = high - low
    price_momentum = np.where(
        price_range != 0,
        ((close - open_) / price_range) * mfi_params.rsi_mfi_multiplier,
        0
    )

    # Apply SMA smoothing
    rsi_mfi_raw = ta.SMA(price_momentum, timeperiod=mfi_params.rsi_mfi_period)

    # Subtract position Y offset (Pine Script: - rsiMFIPosY)
    rsi_mfi = rsi_mfi_raw - mfi_params.rsi_mfi_pos_y

    return rsi_mfi


def f_stochrsi(
    src: np.ndarray,
    params: Optional[VuManChuParams] = None
) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """
    Stochastic RSI calculation with K and D smoothing

    Args:
        src: Price source data
        params: VuManChu parameter set (uses DEFAULT_PARAMS if None)

    Returns:
        Tuple containing: (stoch_k, stoch_d, stoch_avg)
    """
    if params is None:
        params = DEFAULT_PARAMS

    stoch_params = params.stochastic

    # Apply logarithmic transformation to source if enabled (Pine Script logic)
    if stoch_params.stoch_use_log:
        src_transformed = np.log(src + 1e-10)  # Add small value to avoid log(0)
    else:
        src_transformed = src.copy()

    # Calculate RSI on transformed source
    rsi = ta.RSI(src_transformed, timeperiod=stoch_params.stoch_rsi_len)

    # Calculate Stochastic of RSI
    # Get rolling min and max of RSI
    rsi_series = pd.Series(rsi)
    rsi_min = rsi_series.rolling(window=stoch_params.stoch_len).min().values
    rsi_max = rsi_series.rolling(window=stoch_params.stoch_len).max().values

    # Calculate %K
    stoch_k_raw = np.where(
        (rsi_max - rsi_min) != 0,
        100 * (rsi - rsi_min) / (rsi_max - rsi_min),
        50  # Default to 50 when range is 0
    )

    # Smooth %K
    stoch_k = ta.SMA(stoch_k_raw, timeperiod=stoch_params.stoch_k_smooth)

    # Calculate %D (smoothed %K)
    stoch_d = ta.SMA(stoch_k, timeperiod=stoch_params.stoch_d_smooth)

    # Calculate average of K and D if enabled
    stoch_avg = (stoch_k + stoch_d) / 2.0 if stoch_params.stoch_avg else stoch_k

    return stoch_k, stoch_d, stoch_avg


def f_tc(
    src: np.ndarray,
    params: Optional[VuManChuParams] = None
) -> np.ndarray:
    """
    Schaff Trend Cycle (STC) calculation

    Args:
        src: Price source data
        params: VuManChu parameter set (uses DEFAULT_PARAMS if None)

    Returns:
        Schaff Trend Cycle values
    """
    if params is None:
        params = DEFAULT_PARAMS

    schaff_params = params.schaff

    # Calculate MACD using EMAs
    fast_ema = ta.EMA(src, timeperiod=schaff_params.tc_fast_length)
    slow_ema = ta.EMA(src, timeperiod=schaff_params.tc_slow_length)
    macd = fast_ema - slow_ema

    # Calculate Stochastic of MACD
    macd_series = pd.Series(macd)
    macd_min = macd_series.rolling(window=schaff_params.tc_length).min().values
    macd_max = macd_series.rolling(window=schaff_params.tc_length).max().values

    # Calculate %K of MACD
    stoch_macd = np.where(
        (macd_max - macd_min) != 0,
        100 * (macd - macd_min) / (macd_max - macd_min),
        50
    )

    # Apply factor smoothing
    factor = schaff_params.tc_factor
    stc = np.zeros_like(stoch_macd)

    # Initialize first value
    stc[0] = stoch_macd[0] if not np.isnan(stoch_macd[0]) else 50

    # Apply exponential smoothing with factor
    for i in range(1, len(stoch_macd)):
        if not np.isnan(stoch_macd[i]):
            stc[i] = stc[i-1] + factor * (stoch_macd[i] - stc[i-1])
        else:
            stc[i] = stc[i-1]

    # Second stochastic calculation on the result
    stc_series = pd.Series(stc)
    stc_min = stc_series.rolling(window=schaff_params.tc_length).min().values
    stc_max = stc_series.rolling(window=schaff_params.tc_length).max().values

    final_stc = np.where(
        (stc_max - stc_min) != 0,
        100 * (stc - stc_min) / (stc_max - stc_min),
        50
    )

    # Final smoothing
    result = np.zeros_like(final_stc)
    result[0] = final_stc[0] if not np.isnan(final_stc[0]) else 50

    for i in range(1, len(final_stc)):
        if not np.isnan(final_stc[i]):
            result[i] = result[i-1] + factor * (final_stc[i] - result[i-1])
        else:
            result[i] = result[i-1]

    return result


def f_top_fractal(src: np.ndarray, len_left: int, len_right: int) -> np.ndarray:
    """
    Detect top fractals (local maxima)

    Args:
        src: Price data array
        len_left: Number of bars to look left
        len_right: Number of bars to look right

    Returns:
        Boolean array indicating fractal positions
    """
    fractals = np.zeros(len(src), dtype=bool)

    for i in range(len_left, len(src) - len_right):
        if np.isnan(src[i]):
            continue

        is_fractal = True
        current_value = src[i]

        # Check left side
        for j in range(i - len_left, i):
            if np.isnan(src[j]) or src[j] >= current_value:
                is_fractal = False
                break

        # Check right side
        if is_fractal:
            for j in range(i + 1, i + len_right + 1):
                if np.isnan(src[j]) or src[j] >= current_value:
                    is_fractal = False
                    break

        fractals[i] = is_fractal

    return fractals


def f_bot_fractal(src: np.ndarray, len_left: int, len_right: int) -> np.ndarray:
    """
    Detect bottom fractals (local minima)

    Args:
        src: Price data array
        len_left: Number of bars to look left
        len_right: Number of bars to look right

    Returns:
        Boolean array indicating fractal positions
    """
    fractals = np.zeros(len(src), dtype=bool)

    for i in range(len_left, len(src) - len_right):
        if np.isnan(src[i]):
            continue

        is_fractal = True
        current_value = src[i]

        # Check left side
        for j in range(i - len_left, i):
            if np.isnan(src[j]) or src[j] <= current_value:
                is_fractal = False
                break

        # Check right side
        if is_fractal:
            for j in range(i + 1, i + len_right + 1):
                if np.isnan(src[j]) or src[j] <= current_value:
                    is_fractal = False
                    break

        fractals[i] = is_fractal

    return fractals


def f_fractalize(src: np.ndarray, len_left: int, len_right: int) -> Tuple[np.ndarray, np.ndarray]:
    """
    Detect both top and bottom fractals

    Args:
        src: Price data array
        len_left: Number of bars to look left
        len_right: Number of bars to look right

    Returns:
        Tuple containing: (top_fractals, bottom_fractals)
    """
    top_fractals = f_top_fractal(src, len_left, len_right)
    bottom_fractals = f_bot_fractal(src, len_left, len_right)

    return top_fractals, bottom_fractals


def f_top_fractal_pine(src: np.ndarray) -> np.ndarray:
    """
    Pine Script compatible top fractal detection
    Pine Script: f_top_fractal(src) => src[4] < src[2] and src[3] < src[2] and src[2] > src[1] and src[2] > src[0]

    Args:
        src: Price data array

    Returns:
        Boolean array indicating fractal positions (at index i-2)
    """
    fractals = np.zeros(len(src), dtype=bool)

    # Pine Script uses 5-period lookback: [4,3,2,1,0] where 2 is the center
    for i in range(4, len(src)):
        # Check Pine Script condition: src[4] < src[2] and src[3] < src[2] and src[2] > src[1] and src[2] > src[0]
        if (src[i-4] < src[i-2] and
            src[i-3] < src[i-2] and
            src[i-2] > src[i-1] and
            src[i-2] > src[i]):
            fractals[i-2] = True  # Mark the fractal at the center position

    return fractals


def f_bot_fractal_pine(src: np.ndarray) -> np.ndarray:
    """
    Pine Script compatible bottom fractal detection
    Pine Script: f_bot_fractal(src) => src[4] > src[2] and src[3] > src[2] and src[2] < src[1] and src[2] < src[0]

    Args:
        src: Price data array

    Returns:
        Boolean array indicating fractal positions (at index i-2)
    """
    fractals = np.zeros(len(src), dtype=bool)

    # Pine Script uses 5-period lookback: [4,3,2,1,0] where 2 is the center
    for i in range(4, len(src)):
        # Check Pine Script condition: src[4] > src[2] and src[3] > src[2] and src[2] < src[1] and src[2] < src[0]
        if (src[i-4] > src[i-2] and
            src[i-3] > src[i-2] and
            src[i-2] < src[i-1] and
            src[i-2] < src[i]):
            fractals[i-2] = True  # Mark the fractal at the center position

    return fractals


def f_fractalize_pine(src: np.ndarray) -> np.ndarray:
    """
    Pine Script compatible fractalize function
    Pine Script: f_fractalize(src) => f_top_fractal(src) ? 1 : f_bot_fractal(src) ? -1 : 0

    Args:
        src: Price data array

    Returns:
        Array with 1 for top fractals, -1 for bottom fractals, 0 otherwise
    """
    top_fractals = f_top_fractal_pine(src)
    bot_fractals = f_bot_fractal_pine(src)

    result = np.zeros(len(src), dtype=int)
    result[top_fractals] = 1
    result[bot_fractals] = -1

    return result


def valuewhen(condition: np.ndarray, source: np.ndarray, occurrence: int = 0) -> np.ndarray:
    """
    Pine Script valuewhen() function equivalent
    Returns the value of source when condition was true, occurrence times ago

    Args:
        condition: Boolean array indicating when to capture values
        source: Source array to get values from
        occurrence: How many occurrences back (0 = most recent)

    Returns:
        Array with values from source when condition was true
    """
    result = np.full(len(source), np.nan)

    for i in range(len(condition)):
        if condition[i]:
            # Find the occurrence-th previous true condition
            count = 0
            for j in range(i, -1, -1):
                if condition[j]:
                    if count == occurrence:
                        result[i] = source[j]
                        break
                    count += 1

    return result


def f_findDivs_pine(
    src: np.ndarray,
    high: np.ndarray,
    low: np.ndarray,
    top_limit: float,
    bot_limit: float,
    use_limits: bool
) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
    """
    Pine Script compatible divergence detection

    Pine Script: f_findDivs(src, topLimit, botLimit, useLimits) =>
        fractalTop = f_fractalize(src) > 0 and (useLimits ? src[2] >= topLimit : true) ? src[2] : na
        fractalBot = f_fractalize(src) < 0 and (useLimits ? src[2] <= botLimit : true) ? src[2] : na
        highPrev = valuewhen(fractalTop, src[2], 0)[2]
        highPrice = valuewhen(fractalTop, high[2], 0)[2]
        lowPrev = valuewhen(fractalBot, src[2], 0)[2]
        lowPrice = valuewhen(fractalBot, low[2], 0)[2]
        bearSignal = fractalTop and high[2] > highPrice and src[2] < highPrev
        bullSignal = fractalBot and low[2] < lowPrice and src[2] > lowPrev
        bearDivHidden = fractalTop and high[2] < highPrice and src[2] > highPrev
        bullDivHidden = fractalBot and low[2] > lowPrice and src[2] < lowPrev
        [fractalTop, fractalBot, lowPrev, bearSignal, bullSignal, bearDivHidden, bullDivHidden]

    Args:
        src: Source indicator data (e.g., WaveTrend, RSI)
        high: High price data
        low: Low price data
        top_limit: Top limit for fractal filtering
        bot_limit: Bottom limit for fractal filtering
        use_limits: Whether to apply limits

    Returns:
        Tuple containing: (fractal_top, fractal_bot, low_prev, bear_signal, bull_signal, bear_div_hidden, bull_div_hidden)
    """
    # Get Pine Script fractals
    fractalize_result = f_fractalize_pine(src)

    # Create fractal conditions with limits
    fractal_top_condition = np.zeros(len(src), dtype=bool)
    fractal_bot_condition = np.zeros(len(src), dtype=bool)
    fractal_top_values = np.full(len(src), np.nan)
    fractal_bot_values = np.full(len(src), np.nan)

    for i in range(2, len(src)):  # Start from index 2 to handle src[2] indexing
        # Pine Script: fractalTop = f_fractalize(src) > 0 and (useLimits ? src[2] >= topLimit : true) ? src[2] : na
        if fractalize_result[i] > 0:  # Top fractal
            if not use_limits or src[i] >= top_limit:
                fractal_top_condition[i] = True
                fractal_top_values[i] = src[i]

        # Pine Script: fractalBot = f_fractalize(src) < 0 and (useLimits ? src[2] <= botLimit : true) ? src[2] : na
        if fractalize_result[i] < 0:  # Bottom fractal
            if not use_limits or src[i] <= bot_limit:
                fractal_bot_condition[i] = True
                fractal_bot_values[i] = src[i]

    # Pine Script: highPrev = valuewhen(fractalTop, src[2], 0)[2]
    high_prev = valuewhen(fractal_top_condition, src, 0)
    # Pine Script: highPrice = valuewhen(fractalTop, high[2], 0)[2]
    high_price = valuewhen(fractal_top_condition, high, 0)
    # Pine Script: lowPrev = valuewhen(fractalBot, src[2], 0)[2]
    low_prev = valuewhen(fractal_bot_condition, src, 0)
    # Pine Script: lowPrice = valuewhen(fractalBot, low[2], 0)[2]
    low_price = valuewhen(fractal_bot_condition, low, 0)

    # Initialize signal arrays
    bear_signal = np.zeros(len(src), dtype=bool)
    bull_signal = np.zeros(len(src), dtype=bool)
    bear_div_hidden = np.zeros(len(src), dtype=bool)
    bull_div_hidden = np.zeros(len(src), dtype=bool)

    for i in range(2, len(src)):  # Handle [2] indexing
        # Pine Script: bearSignal = fractalTop and high[2] > highPrice and src[2] < highPrev
        if (fractal_top_condition[i] and
            not np.isnan(high_price[i]) and not np.isnan(high_prev[i]) and
            high[i] > high_price[i] and src[i] < high_prev[i]):
            bear_signal[i] = True

        # Pine Script: bullSignal = fractalBot and low[2] < lowPrice and src[2] > lowPrev
        if (fractal_bot_condition[i] and
            not np.isnan(low_price[i]) and not np.isnan(low_prev[i]) and
            low[i] < low_price[i] and src[i] > low_prev[i]):
            bull_signal[i] = True

        # Pine Script: bearDivHidden = fractalTop and high[2] < highPrice and src[2] > highPrev
        if (fractal_top_condition[i] and
            not np.isnan(high_price[i]) and not np.isnan(high_prev[i]) and
            high[i] < high_price[i] and src[i] > high_prev[i]):
            bear_div_hidden[i] = True

        # Pine Script: bullDivHidden = fractalBot and low[2] > lowPrice and src[2] < lowPrev
        if (fractal_bot_condition[i] and
            not np.isnan(low_price[i]) and not np.isnan(low_prev[i]) and
            low[i] > low_price[i] and src[i] < low_prev[i]):
            bull_div_hidden[i] = True

    return (fractal_top_values, fractal_bot_values, low_prev, bear_signal, bull_signal, bear_div_hidden, bull_div_hidden)


# Keep the original f_findDivs for backward compatibility
def f_findDivs(
    price: np.ndarray,
    indicator: np.ndarray,
    params: Optional[VuManChuParams] = None
) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
    """
    Original divergence detection function (kept for backward compatibility)
    """
    if params is None:
        params = DEFAULT_PARAMS

    div_params = params.divergence

    # Get fractals using original method
    price_top_fractals, price_bot_fractals = f_fractalize(price, div_params.lbL, div_params.lbR)
    ind_top_fractals, ind_bot_fractals = f_fractalize(indicator, div_params.lbL, div_params.lbR)

    # Initialize divergence arrays
    bullish_div = np.zeros(len(price), dtype=bool)
    bearish_div = np.zeros(len(price), dtype=bool)
    hidden_bullish_div = np.zeros(len(price), dtype=bool)
    hidden_bearish_div = np.zeros(len(price), dtype=bool)

    # Find divergences using original logic
    for i in range(div_params.rangeUpper, len(price)):
        start_idx = max(0, i - div_params.rangeUpper)
        end_idx = max(0, i - div_params.rangeLower)

        if price_bot_fractals[i] and ind_bot_fractals[i]:
            for j in range(start_idx, end_idx):
                if price_bot_fractals[j] and ind_bot_fractals[j]:
                    if price[i] < price[j] and indicator[i] > indicator[j]:
                        bullish_div[i] = True
                    elif price[i] > price[j] and indicator[i] < indicator[j]:
                        hidden_bullish_div[i] = True
                    break

        if price_top_fractals[i] and ind_top_fractals[i]:
            for j in range(start_idx, end_idx):
                if price_top_fractals[j] and ind_top_fractals[j]:
                    if price[i] > price[j] and indicator[i] < indicator[j]:
                        bearish_div[i] = True
                    elif price[i] < price[j] and indicator[i] > indicator[j]:
                        hidden_bearish_div[i] = True
                    break

    return bullish_div, bearish_div, hidden_bullish_div, hidden_bearish_div


def f_findSommiFlag(
    tf: str,
    wt1: np.ndarray,
    wt2: np.ndarray,
    rsi_mfi: np.ndarray,
    wt_cross: np.ndarray,
    wt_cross_up: np.ndarray,
    wt_cross_down: np.ndarray,
    params: Optional[VuManChuParams] = None
) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """
    Pine Script compatible Sommi Flag detection

    Pine Script: f_findSommiFlag(tf, wt1, wt2, rsimfi, wtCross, wtCrossUp, wtCrossDown) =>
        [hwt1, hwt2, hwtOversold, hwtOverbought, hwtCross, hwtCrossUp, hwtCrossDown, hwtCrosslast, hwtCrossUplast, hwtCrossDownlast, hwtVwap] = f_wavetrend(wtMASource, wtChannelLen, wtAverageLen, wtMALen, tf)

        bearPattern = rsimfi < soomiRSIMFIBearLevel and
                       wt2 > soomiFlagWTBearLevel and
                       wtCross and
                       wtCrossDown and
                       hwtVwap < sommiVwapBearLevel

        bullPattern = rsimfi > soomiRSIMFIBullLevel and
                       wt2 < soomiFlagWTBullLevel and
                       wtCross and
                       wtCrossUp and
                       hwtVwap > sommiVwapBullLevel

        [bearPattern, bullPattern, hwtVwap]

    Args:
        tf: Higher timeframe for VWAP calculation
        wt1: Current timeframe WaveTrend 1 values
        wt2: Current timeframe WaveTrend 2 values
        rsi_mfi: RSI+MFI area values
        wt_cross: WaveTrend cross signals
        wt_cross_up: WaveTrend cross up signals
        wt_cross_down: WaveTrend cross down signals
        params: VuManChu parameter set

    Returns:
        Tuple containing: (bear_pattern, bull_pattern, htf_vwap)
    """
    if params is None:
        params = DEFAULT_PARAMS

    sommi_params = params.sommi

    # Calculate higher timeframe WaveTrend to get VWAP
    # TODO: Implement proper higher timeframe data conversion for tf parameter
    # For now, use current timeframe data - this is a limitation
    # In Pine Script: f_wavetrend(wtMASource, wtChannelLen, wtAverageLen, wtMALen, tf)

    # Create dummy price data for HTF calculation (limitation of current implementation)
    dummy_src = np.ones_like(wt1) * 100  # Placeholder
    (_, _, _, _, _, _, _, _, _, _, htf_vwap) = f_wavetrend(dummy_src, params, tf)

    # Initialize pattern arrays
    bear_pattern = np.zeros(len(wt1), dtype=bool)
    bull_pattern = np.zeros(len(wt1), dtype=bool)

    # Pine Script pattern detection
    for i in range(len(wt1)):
        # Pine Script: bearPattern = rsimfi < soomiRSIMFIBearLevel and wt2 > soomiFlagWTBearLevel and wtCross and wtCrossDown and hwtVwap < sommiVwapBearLevel
        if (rsi_mfi[i] < sommi_params.sommi_rsi_mfi_bear_level and
            wt2[i] > sommi_params.sommi_flag_wt_bear_level and
            wt_cross[i] and
            wt_cross_down[i] and
            htf_vwap[i] < sommi_params.sommi_vwap_bear_level):
            bear_pattern[i] = True

        # Pine Script: bullPattern = rsimfi > soomiRSIMFIBullLevel and wt2 < soomiFlagWTBullLevel and wtCross and wtCrossUp and hwtVwap > sommiVwapBullLevel
        if (rsi_mfi[i] > sommi_params.sommi_rsi_mfi_bull_level and
            wt2[i] < sommi_params.sommi_flag_wt_bull_level and
            wt_cross[i] and
            wt_cross_up[i] and
            htf_vwap[i] > sommi_params.sommi_vwap_bull_level):
            bull_pattern[i] = True

    return bear_pattern, bull_pattern, htf_vwap


def f_getTFCandle(tf: str, open_: np.ndarray, high: np.ndarray, low: np.ndarray, close: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
    """
    Pine Script compatible higher timeframe candle analysis

    Pine Script: f_getTFCandle(_tf) =>
        _open  = security(heikinashi(syminfo.tickerid), _tf, open, barmerge.gaps_off, barmerge.lookahead_on)
        _close = security(heikinashi(syminfo.tickerid), _tf, close, barmerge.gaps_off, barmerge.lookahead_on)
        _high  = security(heikinashi(syminfo.tickerid), _tf, high, barmerge.gaps_off, barmerge.lookahead_on)
        _low   = security(heikinashi(syminfo.tickerid), _tf, low, barmerge.gaps_off, barmerge.lookahead_on)
        hl2   = (_high + _low) / 2.0
        newBar = change(_open)
        candleBodyDir = _close > _open
        [candleBodyDir, newBar]

    Args:
        tf: Timeframe string
        open_: Open price data
        high: High price data
        low: Low price data
        close: Close price data

    Returns:
        Tuple containing: (candle_body_dir, new_bar)
    """
    # Calculate Heiken Ashi candles (simplified for current timeframe)
    # TODO: Implement proper higher timeframe data conversion
    ha_close = (open_ + high + low + close) / 4
    ha_open = np.zeros_like(close)
    ha_open[0] = (open_[0] + close[0]) / 2

    for i in range(1, len(close)):
        ha_open[i] = (ha_open[i-1] + ha_close[i-1]) / 2

    # Determine candle direction
    candle_body_dir = ha_close > ha_open

    # New bar detection (simplified)
    new_bar = np.ones_like(close, dtype=bool)  # Simplified - every bar is "new"

    return candle_body_dir, new_bar


def f_findSommiDiamond(
    tf: str,
    tf2: str,
    wt1: np.ndarray,
    wt2: np.ndarray,
    wt_cross: np.ndarray,
    wt_cross_up: np.ndarray,
    wt_cross_down: np.ndarray,
    open_: np.ndarray,
    high: np.ndarray,
    low: np.ndarray,
    close: np.ndarray,
    params: Optional[VuManChuParams] = None
) -> Tuple[np.ndarray, np.ndarray]:
    """
    Pine Script compatible Sommi Diamond detection

    Pine Script: f_findSommiDiamond(tf, tf2, wt1, wt2, wtCross, wtCrossUp, wtCrossDown) =>
        [candleBodyDir, newBar] = f_getTFCandle(tf)
        [candleBodyDir2, newBar2] = f_getTFCandle(tf2)
        bearPattern = wt2 >= soomiDiamondWTBearLevel and
                       wtCross and
                       wtCrossDown and
                       not candleBodyDir and
                       not candleBodyDir2
        bullPattern = wt2 <= soomiDiamondWTBullLevel and
                       wtCross and
                       wtCrossUp and
                       candleBodyDir and
                       candleBodyDir2
        [bearPattern, bullPattern]

    Args:
        tf: First higher timeframe
        tf2: Second higher timeframe
        wt1: WaveTrend 1 values
        wt2: WaveTrend 2 values
        wt_cross: WaveTrend cross signals
        wt_cross_up: WaveTrend cross up signals
        wt_cross_down: WaveTrend cross down signals
        open_: Open price data
        high: High price data
        low: Low price data
        close: Close price data
        params: VuManChu parameter set

    Returns:
        Tuple containing: (bear_pattern, bull_pattern)
    """
    if params is None:
        params = DEFAULT_PARAMS

    sommi_params = params.sommi

    # Get higher timeframe candle directions
    candle_body_dir, new_bar = f_getTFCandle(tf, open_, high, low, close)
    candle_body_dir2, new_bar2 = f_getTFCandle(tf2, open_, high, low, close)

    # Initialize pattern arrays
    bear_pattern = np.zeros(len(wt1), dtype=bool)
    bull_pattern = np.zeros(len(wt1), dtype=bool)

    # Pine Script pattern detection
    for i in range(len(wt1)):
        # Pine Script: bearPattern = wt2 >= soomiDiamondWTBearLevel and wtCross and wtCrossDown and not candleBodyDir and not candleBodyDir2
        if (wt2[i] >= sommi_params.sommi_diamond_wt_bear_level and
            wt_cross[i] and
            wt_cross_down[i] and
            not candle_body_dir[i] and
            not candle_body_dir2[i]):
            bear_pattern[i] = True

        # Pine Script: bullPattern = wt2 <= soomiDiamondWTBullLevel and wtCross and wtCrossUp and candleBodyDir and candleBodyDir2
        if (wt2[i] <= sommi_params.sommi_diamond_wt_bull_level and
            wt_cross[i] and
            wt_cross_up[i] and
            candle_body_dir[i] and
            candle_body_dir2[i]):
            bull_pattern[i] = True

    return bear_pattern, bull_pattern


def generate_signals(
    open_: np.ndarray,
    high: np.ndarray,
    low: np.ndarray,
    close: np.ndarray,
    params: Optional[VuManChuParams] = None
) -> dict:
    """
    Generate all VuManChu Cipher B signals and indicators

    Args:
        high: High price data
        low: Low price data
        close: Close price data
        params: VuManChu parameter set (uses DEFAULT_PARAMS if None)

    Returns:
        Dictionary containing all calculated indicators and signals
    """
    if params is None:
        params = DEFAULT_PARAMS

    # Get price source for WaveTrend
    wt_src = get_price_source(params.wavetrend.wt_ma_source, open_, high, low, close)

    # Calculate WaveTrend
    (wt1, wt2, wt_oversold, wt_overbought, wt_cross,
     wt_cross_up, wt_cross_down, wt_cross_last, wt_cross_up_last,
     wt_cross_down_last, wt_vwap) = f_wavetrend(wt_src, params)

    # Calculate RSI + MFI
    rsi_mfi = f_rsimfi(open_, high, low, close, params)

    # Calculate Stochastic RSI
    stoch_k, stoch_d, stoch_avg = f_stochrsi(close, params)

    # Calculate Schaff Trend Cycle
    stc = f_tc(close, params)

    # Calculate divergences
    bullish_div, bearish_div, hidden_bullish_div, hidden_bearish_div = f_findDivs(
        close, wt2, params
    )

    # Calculate Sommi patterns
    sommi_flag_bearish, sommi_flag_bullish, htf_vwap = f_findSommiFlag(
        params.sommi.sommi_vwap_tf, wt1, wt2, rsi_mfi, wt_cross, wt_cross_up, wt_cross_down, params
    )
    sommi_diamond_bearish, sommi_diamond_bullish = f_findSommiDiamond(
        params.sommi.sommi_htc_res, params.sommi.sommi_htc_res2, wt1, wt2,
        wt_cross, wt_cross_up, wt_cross_down, open_, high, low, close, params
    )

    # Generate buy/sell signals
    buy_signals = np.zeros(len(close), dtype=bool)
    sell_signals = np.zeros(len(close), dtype=bool)
    gold_signals = np.zeros(len(close), dtype=bool)

    # Calculate RSI for gold signal condition
    rsi = ta.RSI(close, timeperiod=params.rsi.rsi_len)

    for i in range(len(close)):
        # Buy signals: WT cross up at oversold levels
        if (wt_cross_up[i] and wt_oversold[i] and
            params.wavetrend.wt_buy_show):
            buy_signals[i] = True

        # Sell signals: WT cross down at overbought levels
        if (wt_cross_down[i] and wt_overbought[i] and
            params.wavetrend.wt_sell_show):
            sell_signals[i] = True

        # Gold signals: RSI < 20, WT <= -80, cross up, with bullish divergence
        if (rsi[i] < 20 and wt2[i] <= -80 and wt_cross_up[i] and
            bullish_div[i] and params.wavetrend.wt_gold_show):
            gold_signals[i] = True

    # Compile all results
    results = {
        # WaveTrend components
        'wt1': wt1,
        'wt2': wt2,
        'wt_vwap': wt_vwap,
        'wt_oversold': wt_oversold,
        'wt_overbought': wt_overbought,
        'wt_cross': wt_cross,
        'wt_cross_up': wt_cross_up,
        'wt_cross_down': wt_cross_down,

        # RSI + MFI
        'rsi_mfi': rsi_mfi,
        'rsi': rsi,

        # Stochastic RSI
        'stoch_k': stoch_k,
        'stoch_d': stoch_d,
        'stoch_avg': stoch_avg,

        # Schaff Trend Cycle
        'stc': stc,

        # Divergences
        'bullish_div': bullish_div,
        'bearish_div': bearish_div,
        'hidden_bullish_div': hidden_bullish_div,
        'hidden_bearish_div': hidden_bearish_div,

        # Sommi patterns
        'sommi_flag_bearish': sommi_flag_bearish,
        'sommi_flag_bullish': sommi_flag_bullish,
        'sommi_diamond_bearish': sommi_diamond_bearish,
        'sommi_diamond_bullish': sommi_diamond_bullish,
        'htf_vwap': htf_vwap,

        # Trading signals
        'buy_signals': buy_signals,
        'sell_signals': sell_signals,
        'gold_signals': gold_signals,

        # Price data
        'high': high,
        'low': low,
        'close': close
    }

    return results
