# VuManChu Cipher B Environment Configuration Example
# Copy this file to .env and fill in your actual values

# =============================================================================
# BINANCE API CONFIGURATION
# =============================================================================
# Get your API credentials from: https://www.binance.com/en/my/settings/api-management
# NOTE: API credentials are OPTIONAL for public market data access
# Only required if you need private account data or higher rate limits

BINANCE_API_KEY=
BINANCE_API_SECRET=

# Binance API Base URLs (usually don't need to change)
BINANCE_API_URL=https://api.binance.com
BINANCE_WS_URL=wss://stream.binance.com:9443

# =============================================================================
# TRADING CONFIGURATION
# =============================================================================
# Default trading pair and timeframe settings

# Primary trading symbol
DEFAULT_SYMBOL=BTCUSDT

# Default timeframe for analysis
# Supported: 1m, 3m, 5m, 15m, 30m, 1h, 2h, 4h, 6h, 8h, 12h, 1d, 3d, 1w, 1M
DEFAULT_INTERVAL=4h

# Historical data lookback period (days)
DEFAULT_LOOKBACK_DAYS=100

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
# Logging level: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO

# Log format
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# Log file path (optional - leave empty to log to console only)
LOG_FILE=

# =============================================================================
# VUMANCHU CIPHER B INDICATOR PARAMETERS
# =============================================================================
# WaveTrend Oscillator Settings
WT_CHANNEL_LENGTH=9
WT_AVERAGE_LENGTH=12
WT_MA_LENGTH=3
WT_MA_SOURCE=hlc3

# Overbought/Oversold Levels
WT_OVERBOUGHT_LEVEL=53
WT_OVERSOLD_LEVEL=-53
WT_OVERBOUGHT_LEVEL_2=60
WT_OVERSOLD_LEVEL_2=-60
WT_OVERBOUGHT_LEVEL_3=100
WT_OVERSOLD_LEVEL_3=-75

# RSI Settings
RSI_LENGTH=14
RSI_OVERBOUGHT=60
RSI_OVERSOLD=30

# Money Flow Index (MFI) Settings
MFI_PERIOD=60
MFI_MULTIPLIER=150
MFI_POS_Y=2.5

# Stochastic RSI Settings
STOCH_LENGTH=14
STOCH_RSI_LENGTH=14
STOCH_K_SMOOTH=3
STOCH_D_SMOOTH=3
STOCH_USE_LOG=true
STOCH_USE_AVERAGE=false

# Schaff Trend Cycle Settings
STC_LENGTH=10
STC_FAST_LENGTH=23
STC_SLOW_LENGTH=50
STC_FACTOR=0.5

# Divergence Detection Settings
DIVERGENCE_LEFT_BARS=5
DIVERGENCE_RIGHT_BARS=5
DIVERGENCE_RANGE_UPPER=60
DIVERGENCE_RANGE_LOWER=5

# Sommi Flag Settings
SOMMI_VWAP_TIMEFRAME=720
SOMMI_VWAP_BEAR_LEVEL=0
SOMMI_VWAP_BULL_LEVEL=0
SOMMI_FLAG_WT_BEAR_LEVEL=0
SOMMI_FLAG_WT_BULL_LEVEL=0
SOMMI_RSI_MFI_BEAR_LEVEL=0
SOMMI_RSI_MFI_BULL_LEVEL=0

# Sommi Diamond Settings
SOMMI_HTC_RES_1=60
SOMMI_HTC_RES_2=240
SOMMI_DIAMOND_WT_BEAR_LEVEL=0
SOMMI_DIAMOND_WT_BULL_LEVEL=0

# =============================================================================
# VISUALIZATION SETTINGS
# =============================================================================
# Chart display options
CHART_WIDTH=1200
CHART_HEIGHT=800
CHART_THEME=dark

# Color scheme (hex colors)
COLOR_WT1=#4994ec
COLOR_WT2=#1f1559
COLOR_VWAP=#ffffff
COLOR_RSI_OVERBOUGHT=#e13e3e
COLOR_RSI_OVERSOLD=#3ee145
COLOR_MFI_ABOVE=#3ee145
COLOR_MFI_BELOW=#ff3d2e

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
# Output file settings
RESULTS_OUTPUT_FILE=vumanchu_results.json
CHART_OUTPUT_FILE=vumanchu_chart.html

# Performance settings
MAX_CANDLES_BUFFER=1000
CALCULATION_TIMEOUT=30

# Real-time streaming settings
ENABLE_REAL_TIME=false
STREAM_RECONNECT_ATTEMPTS=5
STREAM_RECONNECT_DELAY=5

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
# Development mode (enables additional debugging)
DEBUG_MODE=false

# Enable parameter validation
VALIDATE_PARAMETERS=true

# Enable performance profiling
ENABLE_PROFILING=false

# Test mode (uses mock data instead of live API)
TEST_MODE=false

# =============================================================================
# SECURITY SETTINGS
# =============================================================================
# Rate limiting (requests per minute)
API_RATE_LIMIT=1200

# Request timeout (seconds)
API_TIMEOUT=10

# Enable SSL verification
SSL_VERIFY=true

# =============================================================================
# BACKUP AND PERSISTENCE
# =============================================================================
# Data backup settings
ENABLE_DATA_BACKUP=false
BACKUP_DIRECTORY=./backups
BACKUP_RETENTION_DAYS=30

# Cache settings
ENABLE_CACHE=true
CACHE_DIRECTORY=./cache
CACHE_EXPIRY_HOURS=24
